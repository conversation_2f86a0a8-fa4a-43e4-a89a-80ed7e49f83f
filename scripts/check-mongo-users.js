const mongoose = require('mongoose');

mongoose.connect('mongodb://localhost:27017/admin', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(async () => {
  console.log('Connected to MongoDB');
  try {
    const users = await mongoose.connection.db.admin().command({ usersInfo: 1 });
    console.log('Users in MongoDB:');
    console.log(JSON.stringify(users, null, 2));
  } catch (err) {
    console.error('Error fetching users:', err);
  } finally {
    mongoose.connection.close();
  }
})
.catch(err => {
  console.error('Connection error:', err);
  mongoose.connection.close();
});