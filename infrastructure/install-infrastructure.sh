#!/bin/bash

# 安装脚本用于安装本地基础设施组件

echo "安装本地基础设施组件..."

# 安装MongoDB
echo "安装MongoDB..."
# 假设使用Homebrew进行安装（适用于macOS）
brew install mongodb-community@6.0
# 创建MongoDB数据和日志目录
mkdir -p infrastructure/mongodb/data infrastructure/mongodb/logs
# 确保MongoDB配置文件（如果需要自定义配置）
# echo "systemLog:
#   destination: file
#   path: ./infrastructure/mongodb/logs/mongod.log
# storage:
#   dbPath: ./infrastructure/mongodb/data
# net:
#   bindIp: 127.0.0.1
#   port: 27017" > infrastructure/mongodb/mongod.conf

# 安装RabbitMQ
echo "安装RabbitMQ..."
brew install rabbitmq
# 创建RabbitMQ数据目录
mkdir -p infrastructure/rabbitmq/data
# 设置环境变量（如果需要）
# echo "export RABBITMQ_MNESIA_BASE=./infrastructure/rabbitmq/data" >> ~/.bash_profile

# 安装Redis
echo "安装Redis..."
brew install redis
# 创建Redis数据目录
mkdir -p infrastructure/redis/data
# 创建Redis配置文件
echo "bind 127.0.0.1
port 6379
daemonize no
pidfile ./infrastructure/redis/redis.pid
loglevel notice
logfile ./infrastructure/redis/redis.log
databases 16
dir ./infrastructure/redis/data" > infrastructure/redis/redis.conf

echo "所有基础设施组件已安装完成。请执行 'start-infrastructure.sh' 启动服务。"