#!/bin/bash

# 启动脚本用于启动所有本地基础设施组件

echo "启动本地基础设施组件..."

# 启动MongoDB
echo "启动MongoDB..."
./infrastructure/mongodb/bin/mongod --dbpath ./infrastructure/mongodb/data --logpath ./infrastructure/mongodb/logs/mongod.log --fork

# 启动InfluxDB (假设已经在本地运行，如果需要重新启动可以添加命令)
echo "InfluxDB已经在本地运行，端口为8086。如果需要重新启动，请手动执行相关命令。"

# 启动RabbitMQ
echo "启动RabbitMQ..."
./infrastructure/rabbitmq/sbin/rabbitmq-server -detached

# 启动Redis
echo "启动Redis..."
./infrastructure/redis/bin/redis-server ./infrastructure/redis/redis.conf --daemonize yes

echo "所有基础设施组件已启动。"